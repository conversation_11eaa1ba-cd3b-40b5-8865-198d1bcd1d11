/********************************************************************
 *  uiHandlers.js - UI Handlers Module
 *  ---------------------------------------------------------------
 *  Event handlers and UI management for the text expander
 *******************************************************************/

import * as dom from '../../domElements.js';
import { getCurrentDocument, getSelectedText, getSelectedRange, updateStatus, handleDocumentChange } from './core.js';
import {
    clearComparisonView,
    replaceSelectedTextWithMarkdown,
    highlightTextDifferences
} from './textProcessing.js';
import { processTextWithGemini } from './ai.js';

// Global variable to track if run all is cancelled
let isRunAllCancelled = false;

// Global variable to track if we're showing a comparison view
let isShowingComparison = false;

/**
 * Check if we're currently showing a comparison view
 */
export function getIsShowingComparison() {
    return isShowingComparison;
}

/**
 * Initialize UI handlers
 */
export function initializeUIHandlers() {
    console.log('Initializing UI handlers...');
    
    // Set up run all formatting functionality
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.addEventListener('click', handleRunAllFormatting);
    }
    
    if (dom.cancelRunAllBtn) {
        dom.cancelRunAllBtn.addEventListener('click', handleCancelRunAll);
    }
}

/**
 * Handle run all formatting
 */
async function handleRunAllFormatting() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }

    // Reset cancellation flag
    isRunAllCancelled = false;

    // Show progress indicator
    showRunAllProgress();

    try {
        updateRunAllProgress(10, "Preparing", "Preparing comprehensive formatting instructions...");

        // Get selected text instead of entire document
        const selectedText = getSelectedText();

        if (!selectedText || selectedText.trim().length === 0) {
            updateStatus("Please select some text to format.", "error");
            hideRunAllProgress();
            return;
        }

        updateRunAllProgress(20, "Processing", "Sending comprehensive formatting request to AI...");

        // Create the master formatting prompt that combines all instructions
        const masterPrompt = createMasterFormattingPrompt();
        console.log('Master prompt being sent to AI:', masterPrompt);
        console.log('Selected text being processed:', selectedText.substring(0, 200) + '...');

        // Process with AI using the master prompt
        const formattedContent = await processTextWithGemini(selectedText, masterPrompt);

        updateRunAllProgress(80, "Preparing Preview", "Preparing comparison view...");

        // Show comparison instead of directly applying changes
        if (formattedContent && formattedContent !== selectedText) {
            updateRunAllProgress(100, "Complete", "Review changes below");

            // Hide progress indicator and show comparison
            setTimeout(() => {
                hideRunAllProgress();
                showRunAllFormattingPreview(selectedText, formattedContent);
            }, 100);

            updateStatus("All formatting completed. Review and accept/reject changes below.", "success");
        } else {
            updateRunAllProgress(100, "Complete", "No changes needed");
            setTimeout(() => {
                hideRunAllProgress();
            }, 1000);
            updateStatus("Text processed - no formatting changes were needed.", "info");
        }

        // Hide progress after a delay
        setTimeout(() => {
            hideRunAllProgress();
        }, 2000);

    } catch (error) {
        console.error("Error in run all formatting:", error);
        updateStatus(`Failed to complete formatting: ${error.message}`, "error");
        hideRunAllProgress();
    }
}

/**
 * Create master formatting prompt that combines all formatting instructions
 */
function createMasterFormattingPrompt() {
    return `You are an expert text formatter for audiobook preparation. You MUST apply ALL of the following formatting rules to the provided text. This is critical - do not skip any rules.

**CRITICAL: SCRIPTURE QUOTE DETECTION AND FORMATTING**
Look for scripture quotes (biblical quotations) which are typically:
- Quoted text followed by a biblical reference like (1 Pet. 1, 18), (Rom. 2:4-5), (Mt. 5:3-4), etc.
- For ANY scripture quote with 8+ words, you MUST:
  1. Add "quote," at the very beginning of the quoted text
  2. Add "end quote," at the very end of the quoted text
  3. Expand the reference: (1 Pet. 1, 18) → (First Peter, chapter 1, verse 18)
  4. Apply **bold formatting** to both the quote and reference

**EXAMPLE TRANSFORMATION:**
BEFORE: 'You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled.' (1 Pet. 1, 18)
AFTER: **quote, 'You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled.' end quote, (First Peter, chapter 1, verse 18)**

**1. ABBREVIATION EXPANSION:**
- Expand common abbreviations: Jan.→January, Dr.→Doctor, St.→Street (when not referring to saints)
- Expand academic titles: Prof.→Professor, Ph.D.→Doctor of Philosophy
- Expand measurements: ft.→feet, in.→inches, lb.→pounds, oz.→ounces
- Expand time references: a.m.→in the morning, p.m.→in the evening

**2. PAPAL & SAINT NAME FORMATTING:**
- Papal names: Leo XIII.→Leo the 13th, Pius XII.→Pius the 12th
- Saint abbreviations: St. Peter→Saint Peter, St. Paul→Saint Paul
- Roman numeral conversion to ordinal words

**3. QUOTE MARKERS FOR WRITTEN WORKS:**
- Add "quote," and "end quote," markers to formal quotations from books/articles (15+ words)
- Do NOT apply to conversational speech or dialogue

**4. FOOTNOTE FORMATTING FOR AUDIOBOOKS:**
- Move footnotes inline with natural flow
- Simplify page ranges: pp 123-126→pages 123 through 126

**MANDATORY REQUIREMENTS:**
- You MUST identify and format ALL scripture quotes according to the example above
- Apply **bold formatting** using markdown syntax (**text**)
- Preserve all original text structure and meaning
- Return ONLY the formatted text, no explanations

CRITICAL: Pay special attention to scripture quotes - they are the most important formatting rule.`;
}

/**
 * Show run all formatting preview with comparison
 */
function showRunAllFormattingPreview(originalText, formattedText) {
    console.log('Showing run all formatting preview...');
    console.log('Original length:', originalText.length);
    console.log('Formatted length:', formattedText.length);

    // Set flag to prevent comparison from being cleared
    isShowingComparison = true;

    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');

    if (selectionDisplay) {
        // Convert markdown to HTML for display
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightTextDifferences(originalText, htmlFormattedText);

        selectionDisplay.innerHTML = `
            <div class="comparison-view">
                <div class="color-legend-inline">
                    <div class="legend-title">Change Indicators:</div>
                    <div class="legend-items-inline">
                        <span class="legend-item-inline"><span class="diff-added">Added</span></span>
                        <span class="legend-item-inline"><span class="diff-changed">Changed</span></span>
                        <span class="legend-item-inline"><span class="diff-removed">Removed</span></span>
                    </div>
                </div>
                <div class="comparison-section">
                    <h4>Original Selected Text:</h4>
                    <div class="text-preview original">${highlightedOriginal}</div>
                </div>
                <div class="comparison-section">
                    <h4>AI Formatted Result (All Rules Applied):</h4>
                    <div class="text-preview formatted">${highlightedFormatted}</div>
                </div>
                <div class="comparison-actions">
                    <button id="accept-run-all-formatting" class="control-button accent-button small">Accept All Formatting</button>
                    <button id="reject-run-all-formatting" class="control-button secondary small">Reject Changes</button>
                </div>
            </div>
        `;

        // Add event listeners for the new buttons
        const acceptBtn = document.getElementById('accept-run-all-formatting');
        const rejectBtn = document.getElementById('reject-run-all-formatting');

        if (acceptBtn) {
            acceptBtn.addEventListener('click', () => {
                // Use markdown replacement to preserve bold formatting
                replaceSelectedTextWithMarkdown(formattedText);
                clearComparisonView();
                isShowingComparison = false;
                updateStatus("All formatting applied successfully.", "success");
            });
        }

        if (rejectBtn) {
            rejectBtn.addEventListener('click', () => {
                clearComparisonView();
                isShowingComparison = false;
                updateStatus("All formatting changes rejected.", "info");
            });
        }
    }
}

/**
 * Replace selected text with formatted version
 */
function replaceSelectedText(formattedText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Insert the formatted text
        const textNode = document.createTextNode(formattedText);
        range.insertNode(textNode);

        // Clear the selection
        window.getSelection().removeAllRanges();

        console.log('Text replaced successfully');
    }
}

/**
 * Handle cancel run all
 */
function handleCancelRunAll() {
    isRunAllCancelled = true;
    updateStatus("Formatting operation cancelled.", "info");
}

/**
 * Show run all progress indicator
 */
function showRunAllProgress() {
    if (dom.runAllProgress) {
        dom.runAllProgress.style.display = 'block';
    }
    
    // Disable the run all button
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.disabled = true;
    }
}

/**
 * Hide run all progress indicator
 */
function hideRunAllProgress() {
    if (dom.runAllProgress) {
        dom.runAllProgress.style.display = 'none';
    }
    
    // Re-enable the run all button
    if (dom.runAllFormattingBtn && getCurrentDocument()) {
        dom.runAllFormattingBtn.disabled = false;
    }
}

/**
 * Update run all progress
 */
function updateRunAllProgress(percentage, title, description) {
    if (dom.progressFill) {
        dom.progressFill.style.width = `${percentage}%`;
    }
    
    if (dom.progressText) {
        dom.progressText.innerHTML = `<strong>${title}</strong><br>${description}`;
    }
}

/**
 * Generate summary of run all results
 */
function generateRunAllSummary(results) {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const withChanges = results.filter(r => r.success && r.hasChanges);
    
    let summary = `Run All Formatting Complete: ${successful.length}/${results.length} steps successful`;
    
    if (withChanges.length > 0) {
        summary += `. Changes applied: ${withChanges.map(r => r.step).join(', ')}`;
    }
    
    if (failed.length > 0) {
        summary += `. Failed: ${failed.map(r => r.step).join(', ')}`;
    }
    
    return summary;
}

/**
 * Initialize dropdown functionality for buttons
 */
export function initializeDropdowns() {
    const dropdownButtons = document.querySelectorAll('.button-with-dropdown');
    
    dropdownButtons.forEach(container => {
        const button = container.querySelector('button');
        const dropdownToggle = container.querySelector('.dropdown-toggle');
        
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                
                // Close other dropdowns
                dropdownButtons.forEach(otherContainer => {
                    if (otherContainer !== container) {
                        otherContainer.classList.remove('expanded');
                    }
                });
                
                // Toggle current dropdown
                container.classList.toggle('expanded');
            });
        }
        
        // Prevent button click when clicking dropdown toggle
        if (button && dropdownToggle) {
            button.addEventListener('click', (e) => {
                if (e.target === dropdownToggle || dropdownToggle.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.button-with-dropdown')) {
            dropdownButtons.forEach(container => {
                container.classList.remove('expanded');
            });
        }
    });
}
