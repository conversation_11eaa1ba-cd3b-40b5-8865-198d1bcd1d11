/********************************************************************
 *  uiHandlers.js - UI Handlers Module
 *  ---------------------------------------------------------------
 *  Event handlers and UI management for the text expander
 *******************************************************************/

import * as dom from '../../domElements.js';
import { getCurrentDocument, getSelectedText, getSelectedRange, updateStatus, handleDocumentChange } from './core.js';
import {
    clearComparisonView,
    replaceSelectedTextWithMarkdown,
    highlightTextDifferences
} from './textProcessing.js';
import { processTextWithGemini } from './ai.js';

// Global variable to track if run all is cancelled
let isRunAllCancelled = false;

// Global variable to track if we're showing a comparison view
let isShowingComparison = false;

/**
 * Check if we're currently showing a comparison view
 */
export function getIsShowingComparison() {
    return isShowingComparison;
}

/**
 * Initialize UI handlers
 */
export function initializeUIHandlers() {
    console.log('Initializing UI handlers...');
    
    // Set up run all formatting functionality
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.addEventListener('click', handleRunAllFormatting);
    }
    
    if (dom.cancelRunAllBtn) {
        dom.cancelRunAllBtn.addEventListener('click', handleCancelRunAll);
    }
}

/**
 * Handle run all formatting
 */
async function handleRunAllFormatting() {
    if (!getCurrentDocument()) {
        updateStatus("No document loaded.", "error");
        return;
    }

    // Reset cancellation flag
    isRunAllCancelled = false;

    // Show progress indicator
    showRunAllProgress();

    try {
        updateRunAllProgress(10, "Preparing", "Preparing comprehensive formatting instructions...");

        // Get selected text instead of entire document
        const selectedText = getSelectedText();

        if (!selectedText || selectedText.trim().length === 0) {
            updateStatus("Please select some text to format.", "error");
            hideRunAllProgress();
            return;
        }

        updateRunAllProgress(20, "Processing", "Sending comprehensive formatting request to AI...");

        // Create the master formatting prompt that combines all instructions
        const masterPrompt = createMasterFormattingPrompt();
        console.log('Master prompt being sent to AI:', masterPrompt);
        console.log('Selected text being processed:', selectedText.substring(0, 200) + '...');

        // Process with AI using the master prompt
        const formattedContent = await processTextWithGemini(selectedText, masterPrompt);

        updateRunAllProgress(80, "Preparing Preview", "Preparing comparison view...");

        // Show comparison instead of directly applying changes
        if (formattedContent && formattedContent !== selectedText) {
            updateRunAllProgress(100, "Complete", "Review changes below");

            // Hide progress indicator and show comparison
            setTimeout(() => {
                hideRunAllProgress();
                showRunAllFormattingPreview(selectedText, formattedContent);
            }, 100);

            updateStatus("All formatting completed. Review and accept/reject changes below.", "success");
        } else {
            updateRunAllProgress(100, "Complete", "No changes needed");
            setTimeout(() => {
                hideRunAllProgress();
            }, 1000);
            updateStatus("Text processed - no formatting changes were needed.", "info");
        }

        // Hide progress after a delay
        setTimeout(() => {
            hideRunAllProgress();
        }, 2000);

    } catch (error) {
        console.error("Error in run all formatting:", error);
        updateStatus(`Failed to complete formatting: ${error.message}`, "error");
        hideRunAllProgress();
    }
}

/**
 * Create master formatting prompt that combines all formatting instructions
 */
function createMasterFormattingPrompt() {
    return `You are an expert text formatter for audiobook preparation. You MUST apply ALL of the following formatting rules to the provided text. This is critical - do not skip any rules.

**CRITICAL: SCRIPTURE QUOTE DETECTION AND FORMATTING**
Look for scripture quotes (biblical quotations) which are typically:
- Quoted text followed by a biblical reference like (1 Pet. 1, 18), (Rom. 2:4-5), (Mt. 5:3-4), etc.
- For ANY scripture quote with 8+ words, you MUST:
  1. Add "quote," at the very beginning of the quoted text
  2. Add "end quote," at the very end of the quoted text
  3. Expand the reference: (1 Pet. 1, 18) → (First Peter, chapter 1, verse 18)
  4. Apply **bold formatting** to both the quote and reference

**EXAMPLE TRANSFORMATION:**
BEFORE: 'You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled.' (1 Pet. 1, 18)
AFTER: **quote, 'You were not redeemed with corruptible things as gold or silver . . . but with the precious blood of Christ, as of a lamb unspotted and undefiled.' end quote, (First Peter, chapter 1, verse 18)**

**1. ABBREVIATION EXPANSION:**
- Expand common abbreviations: Jan.→January, Dr.→Doctor, St.→Street (when not referring to saints)
- Expand academic titles: Prof.→Professor, Ph.D.→Doctor of Philosophy
- Expand measurements: ft.→feet, in.→inches, lb.→pounds, oz.→ounces
- Expand time references: a.m.→in the morning, p.m.→in the evening

**2. PAPAL & SAINT NAME FORMATTING:**
- Papal names: Leo XIII.→Leo the 13th, Pius XII.→Pius the 12th
- Saint abbreviations: St. Peter→Saint Peter, St. Paul→Saint Paul
- Roman numeral conversion to ordinal words

**3. QUOTE MARKERS FOR WRITTEN WORKS:**
- Add "quote," and "end quote," markers to formal quotations from books/articles (15+ words)
- Do NOT apply to conversational speech or dialogue

**4. FOOTNOTE FORMATTING FOR AUDIOBOOKS:**
- Move footnotes inline with natural flow
- Simplify page ranges: pp 123-126→pages 123 through 126

**MANDATORY REQUIREMENTS:**
- You MUST identify and format ALL scripture quotes according to the example above
- Apply **bold formatting** using markdown syntax (**text**)
- Preserve all original text structure and meaning
- Return ONLY the formatted text, no explanations

CRITICAL: Pay special attention to scripture quotes - they are the most important formatting rule.`;
}

/**
 * Show run all formatting preview with comparison using the modal
 */
function showRunAllFormattingPreview(originalText, formattedText) {
    console.log('Showing run all formatting preview in modal...');
    console.log('Original length:', originalText.length);
    console.log('Formatted length:', formattedText.length);

    // Set flag to prevent comparison from being cleared
    isShowingComparison = true;

    // Get modal elements
    const modal = document.getElementById('text-expansion-preview-modal');
    const modalTitle = modal?.querySelector('h3');
    const originalDisplay = document.getElementById('original-text-display');
    const expandedDisplay = document.getElementById('expanded-text-display');

    console.log('Modal found:', !!modal);
    console.log('Original display found:', !!originalDisplay);
    console.log('Expanded display found:', !!expandedDisplay);

    if (modal && modalTitle && originalDisplay && expandedDisplay) {
        // Update modal title
        modalTitle.textContent = 'Run All Formatting Preview';

        // Convert markdown to HTML for display
        let htmlFormattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Generate highlighted versions showing differences
        const { highlightedOriginal, highlightedFormatted } = highlightTextDifferences(originalText, htmlFormattedText);

        console.log('Setting modal content...');

        // Show highlighted original text
        originalDisplay.innerHTML = highlightedOriginal;

        // Show highlighted formatted text
        expandedDisplay.innerHTML = highlightedFormatted;

        // Store the formatted text for later use (use markdownText since Run All Formatting produces markdown)
        expandedDisplay.dataset.markdownText = formattedText;

        // Clear other dataset properties to ensure proper handling
        delete expandedDisplay.dataset.expandedText;
        delete expandedDisplay.dataset.formattedText;

        // Add color legend to modal if it doesn't exist
        addColorLegendToModal(modal);

        // Show the modal
        modal.style.display = 'block';

        console.log('Modal displayed successfully');

        // Update the selection display as well to show we're in comparison mode
        updateSelectionDisplayWithRunAllComparison(originalText, formattedText);

        // Set up global reset function for the modal handlers
        window.resetRunAllComparisonFlag = () => {
            isShowingComparison = false;
            console.log('Run All Formatting comparison flag reset');
        };
    } else {
        console.error('Modal elements not found');
        // Fallback to status message
        updateStatus("Comparison view could not be displayed. Modal elements missing.", "error");
        isShowingComparison = false;
    }
}

/**
 * Add color legend to modal if it doesn't exist
 */
function addColorLegendToModal(modal) {
    // Check if legend already exists
    let legend = modal.querySelector('.color-legend');

    if (!legend) {
        // Create and add legend
        legend = document.createElement('div');
        legend.className = 'color-legend';
        legend.innerHTML = `
            <div class="legend-title">Change Indicators:</div>
            <div class="legend-items">
                <div class="legend-item">
                    <span class="diff-added">Added text</span>
                    <span class="legend-description">New content added by AI</span>
                </div>
                <div class="legend-item">
                    <span class="diff-changed">Changed text</span>
                    <span class="legend-description">Modified content</span>
                </div>
                <div class="legend-item">
                    <span class="diff-removed">Removed text</span>
                    <span class="legend-description">Content that was removed</span>
                </div>
            </div>
        `;

        // Insert legend before the first text display
        const firstDisplay = modal.querySelector('.text-display');
        if (firstDisplay) {
            firstDisplay.parentNode.insertBefore(legend, firstDisplay);
        }
    }
}

/**
 * Update selection display to show we're in run all comparison mode
 */
function updateSelectionDisplayWithRunAllComparison(originalText, formattedText) {
    const selectionDisplay = document.getElementById('abbreviated-text-expander-selection-display');

    if (selectionDisplay) {
        const wordCount = originalText.split(/\s+/).filter(word => word.length > 0).length;
        const charCount = originalText.length;

        selectionDisplay.innerHTML = `
            <div class="comparison-mode-info">
                <h4>🚀 Run All Formatting Active</h4>
                <div class="selection-stats">
                    <span>${wordCount} words, ${charCount} characters</span>
                </div>
                <div class="comparison-status">
                    <p>✨ AI formatting complete!</p>
                    <p>📋 Review changes in the modal above</p>
                    <p>✅ Accept or ❌ Reject when ready</p>
                </div>
            </div>
        `;
    }
}

/**
 * Replace selected text with formatted version
 */
function replaceSelectedText(formattedText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Insert the formatted text
        const textNode = document.createTextNode(formattedText);
        range.insertNode(textNode);

        // Clear the selection
        window.getSelection().removeAllRanges();

        console.log('Text replaced successfully');
    }
}

/**
 * Handle cancel run all
 */
function handleCancelRunAll() {
    isRunAllCancelled = true;
    updateStatus("Formatting operation cancelled.", "info");
}

/**
 * Show run all progress indicator
 */
function showRunAllProgress() {
    if (dom.runAllProgress) {
        dom.runAllProgress.style.display = 'block';
    }
    
    // Disable the run all button
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.disabled = true;
    }
}

/**
 * Hide run all progress indicator
 */
function hideRunAllProgress() {
    if (dom.runAllProgress) {
        dom.runAllProgress.style.display = 'none';
    }
    
    // Re-enable the run all button
    if (dom.runAllFormattingBtn && getCurrentDocument()) {
        dom.runAllFormattingBtn.disabled = false;
    }
}

/**
 * Update run all progress
 */
function updateRunAllProgress(percentage, title, description) {
    if (dom.progressFill) {
        dom.progressFill.style.width = `${percentage}%`;
    }
    
    if (dom.progressText) {
        dom.progressText.innerHTML = `<strong>${title}</strong><br>${description}`;
    }
}

/**
 * Generate summary of run all results
 */
function generateRunAllSummary(results) {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const withChanges = results.filter(r => r.success && r.hasChanges);
    
    let summary = `Run All Formatting Complete: ${successful.length}/${results.length} steps successful`;
    
    if (withChanges.length > 0) {
        summary += `. Changes applied: ${withChanges.map(r => r.step).join(', ')}`;
    }
    
    if (failed.length > 0) {
        summary += `. Failed: ${failed.map(r => r.step).join(', ')}`;
    }
    
    return summary;
}

/**
 * Initialize dropdown functionality for buttons
 */
export function initializeDropdowns() {
    const dropdownButtons = document.querySelectorAll('.button-with-dropdown');
    
    dropdownButtons.forEach(container => {
        const button = container.querySelector('button');
        const dropdownToggle = container.querySelector('.dropdown-toggle');
        
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                
                // Close other dropdowns
                dropdownButtons.forEach(otherContainer => {
                    if (otherContainer !== container) {
                        otherContainer.classList.remove('expanded');
                    }
                });
                
                // Toggle current dropdown
                container.classList.toggle('expanded');
            });
        }
        
        // Prevent button click when clicking dropdown toggle
        if (button && dropdownToggle) {
            button.addEventListener('click', (e) => {
                if (e.target === dropdownToggle || dropdownToggle.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.button-with-dropdown')) {
            dropdownButtons.forEach(container => {
                container.classList.remove('expanded');
            });
        }
    });
}
