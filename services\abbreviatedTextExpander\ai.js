/********************************************************************
 *  ai.js - AI Integration Module
 *  ---------------------------------------------------------------
 *  Google Gemini AI integration for text processing
 *******************************************************************/

import * as dom from '../../domElements.js';
import { GEMINI_MODELS, DEFAULT_GEMINI_MODEL } from '../../constants.js';
import { getCurrentDocument, getSelectedText, getGeminiApiKey, setGeminiApi<PERSON><PERSON>, updateStatus, handleDocument<PERSON>hange, updateButtonStates } from './core.js';
import { extractApiKeyFromInput } from './utils.js';

// AI Prompt Templates
const PROMPT_TEMPLATES = {
    default: {
        name: "Standard Expansion",
        description: "Expands common abbreviations and acronyms",
        prompt: `Please expand any abbreviations, acronyms, or shortened words in the following text while maintaining the original meaning and context. Only expand terms that are clearly abbreviated or shortened. Do not rewrite or change the overall content, just expand the abbreviated terms.

Examples of what to expand:
- Acronyms: USA → United States of America, NASA → National Aeronautics and Space Administration
- Common abbreviations: Dr. → Doctor, Mr. → Mister, etc. → et cetera
- Technical abbreviations: API → Application Programming Interface, CPU → Central Processing Unit
- Time/Date: Jan → January, Mon → Monday, AM/PM → ante meridiem/post meridiem

Do NOT expand:
- Proper names or brand names
- Well-known abbreviations that are commonly used (like "TV" for television)
- Technical terms that are better known by their abbreviation

Text to process: "{TEXT}"

Please respond with only the expanded text, no additional commentary.`
    },

    academic: {
        name: "Academic/Formal",
        description: "Formal academic writing style",
        prompt: `Please expand abbreviations in the following text using formal academic writing conventions. Maintain scholarly tone and precision.

Text to process: "{TEXT}"

Please respond with only the expanded text, no additional commentary.`
    },

    conversational: {
        name: "Conversational",
        description: "Natural, conversational style",
        prompt: `Please expand abbreviations in the following text using natural, conversational language that flows well when spoken aloud.

Text to process: "{TEXT}"

Please respond with only the expanded text, no additional commentary.`
    },

    technical: {
        name: "Technical",
        description: "Technical documentation style",
        prompt: `Please expand abbreviations in the following text using precise technical terminology appropriate for technical documentation.

Text to process: "{TEXT}"

Please respond with only the expanded text, no additional commentary.`
    },

    simple: {
        name: "Simple",
        description: "Clear and accessible language",
        prompt: `Please expand abbreviations in the following text using simple, clear language that is accessible to all readers.

Text to process: "{TEXT}"

Please respond with only the expanded text, no additional commentary.`
    }
};

let currentPromptTemplate = 'default';
let customInstructions = '';

/**
 * Initialize AI integration
 */
export function initializeAI() {
    console.log('Initializing AI integration...');
    
    // Set up event listeners
    if (dom.geminiApiKeyInput) {
        dom.geminiApiKeyInput.addEventListener('input', handleApiKeyInput);
        dom.geminiApiKeyInput.addEventListener('paste', handleApiKeyPaste);
    }
    
    if (dom.testGeminiConnectionBtn) {
        dom.testGeminiConnectionBtn.addEventListener('click', handleTestConnection);
    }
    
    if (dom.expandAbbreviatedTextBtn) {
        dom.expandAbbreviatedTextBtn.addEventListener('click', handleExpandAbbreviatedText);
    }
    
    if (dom.geminiModelSelect) {
        dom.geminiModelSelect.addEventListener('change', handleModelChange);
    }
    
    if (dom.expansionStyleSelect) {
        dom.expansionStyleSelect.addEventListener('change', handleStyleChange);
    }
    
    if (dom.customInstructionsInput) {
        dom.customInstructionsInput.addEventListener('input', handleCustomInstructionsChange);
    }
    
    // Initialize with saved API key if available
    const savedApiKey = getGeminiApiKey();
    if (savedApiKey && dom.geminiApiKeyInput) {
        dom.geminiApiKeyInput.value = savedApiKey;
        console.log('Initialized Gemini API key from constants:', savedApiKey ? `${savedApiKey.substring(0, 10)}...` : 'none');
    }

    // Update button states after API key initialization
    updateButtonStates();

    // Update instructions preview
    updateInstructionsPreview();
}

/**
 * Handle API key input
 */
function handleApiKeyInput(event) {
    let rawInput = event.target.value.trim();
    
    // Extract API key from URL if user pasted a Google AI Studio URL
    const apiKey = extractApiKeyFromInput(rawInput);
    
    console.log('API key updated:', apiKey ? `${apiKey.substring(0, 10)}...` : 'empty');
    
    // Update the input field with the clean API key
    if (dom.geminiApiKeyInput && apiKey !== rawInput) {
        dom.geminiApiKeyInput.value = apiKey;
    }
    
    setGeminiApiKey(apiKey);
    updateButtonStates();
}

/**
 * Handle API key paste
 */
function handleApiKeyPaste() {
    // Use setTimeout to ensure the pasted content is processed
    setTimeout(() => {
        if (dom.geminiApiKeyInput) {
            let rawInput = dom.geminiApiKeyInput.value.trim();
            const apiKey = extractApiKeyFromInput(rawInput);
            
            console.log('API key pasted:', apiKey ? `${apiKey.substring(0, 10)}...` : 'empty');
            
            // Update the input field with the clean API key
            if (apiKey !== rawInput) {
                dom.geminiApiKeyInput.value = apiKey;
            }
            
            setGeminiApiKey(apiKey);
            updateButtonStates();
        }
    }, 10);
}

/**
 * Handle model change
 */
function handleModelChange(event) {
    const selectedModel = event.target.value;
    console.log('Model changed to:', selectedModel);
    updateInstructionsPreview();
}

/**
 * Handle style change
 */
function handleStyleChange(event) {
    currentPromptTemplate = event.target.value;
    console.log('Style changed to:', currentPromptTemplate);
    updateInstructionsPreview();
}

/**
 * Handle custom instructions change
 */
function handleCustomInstructionsChange(event) {
    customInstructions = event.target.value.trim();
    console.log('Custom instructions updated');
    updateInstructionsPreview();
}

/**
 * Test Gemini connection
 */
export async function testGeminiConnection() {
    const apiKey = getGeminiApiKey();
    
    if (!apiKey) {
        updateStatus("Please enter your Google Gemini API key first.", "error");
        return;
    }
    
    // Validate API key format
    if (!apiKey.startsWith('AIza')) {
        if (apiKey.startsWith('http')) {
            updateStatus("Please paste only the API key, not the full URL. The API key should start with 'AIza'.", "error");
        } else {
            updateStatus("Invalid API key format. Google Gemini API keys should start with 'AIza' and be about 39 characters long.", "error");
        }
        return;
    }
    
    if (apiKey.length < 35 || apiKey.length > 45) {
        updateStatus("Invalid API key length. Google Gemini API keys should be about 39 characters long.", "error");
        return;
    }
    
    updateStatus("Testing connection to Google Gemini...", "info");
    
    try {
        const selectedModel = dom.geminiModelSelect?.value || DEFAULT_GEMINI_MODEL;
        const modelConfig = GEMINI_MODELS[selectedModel];
        
        if (!modelConfig) {
            updateStatus(`Unknown model: ${selectedModel}`, "error");
            return;
        }
        
        const response = await fetch(`${modelConfig.url}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: "Hello! Please respond with 'Connection successful' to confirm the API is working."
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    maxOutputTokens: 50
                }
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            const responseText = data.candidates[0].content.parts[0].text;
            updateStatus(`✅ Connection successful! Model: ${modelConfig.name}. Response: "${responseText}"`, "success");
        } else {
            updateStatus("⚠️ Connection established but received unexpected response format.", "warning");
        }
        
    } catch (error) {
        console.error('Connection test failed:', error);
        updateStatus(`❌ Connection failed: ${error.message}`, "error");
    }
}

/**
 * Handle test connection button click
 */
function handleTestConnection() {
    console.log('Test connection clicked');
    console.log('Current API key:', getGeminiApiKey() ? `${getGeminiApiKey().substring(0, 10)}...` : 'none');
    testGeminiConnection();
}

/**
 * Expand abbreviated text using AI
 */
export async function expandAbbreviatedText() {
    const selectedText = getSelectedText();
    const apiKey = getGeminiApiKey();
    
    if (!selectedText) {
        updateStatus("Please select some text to expand.", "error");
        return;
    }
    
    if (!apiKey) {
        updateStatus("Please enter your Google Gemini API key first.", "error");
        return;
    }
    
    updateStatus("Expanding abbreviated text...", "info");
    
    try {
        const selectedModel = dom.geminiModelSelect?.value || DEFAULT_GEMINI_MODEL;
        const modelConfig = GEMINI_MODELS[selectedModel];
        
        if (!modelConfig) {
            updateStatus(`Unknown model: ${selectedModel}`, "error");
            return;
        }
        
        // Build the prompt
        const prompt = buildPrompt(selectedText);
        
        const response = await fetch(`${modelConfig.url}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.3,
                    maxOutputTokens: 2048
                }
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            const expandedText = data.candidates[0].content.parts[0].text.trim();
            
            // Replace the selected text with the expanded version
            replaceSelectedText(expandedText);
            
            updateStatus(`✅ Text expanded successfully using ${modelConfig.name}`, "success");
        } else {
            updateStatus("⚠️ Received unexpected response format from AI.", "warning");
        }
        
    } catch (error) {
        console.error('Text expansion failed:', error);
        updateStatus(`❌ Text expansion failed: ${error.message}`, "error");
    }
}

/**
 * Handle expand abbreviated text button click
 */
function handleExpandAbbreviatedText() {
    expandAbbreviatedText();
}

/**
 * Build prompt for AI
 */
function buildPrompt(text) {
    const template = PROMPT_TEMPLATES[currentPromptTemplate] || PROMPT_TEMPLATES.default;
    let prompt = template.prompt.replace('{TEXT}', text);
    
    // Add custom instructions if provided
    if (customInstructions) {
        prompt += `\n\nAdditional instructions: ${customInstructions}`;
    }
    
    return prompt;
}

/**
 * Process text with Gemini AI using a custom prompt
 */
export async function processTextWithGemini(text, customPrompt) {
    const apiKey = getGeminiApiKey();

    if (!apiKey) {
        throw new Error("Please enter your Google Gemini API key first.");
    }

    if (!text || text.trim().length === 0) {
        throw new Error("No text provided to process.");
    }

    try {
        const selectedModel = dom.geminiModelSelect?.value || DEFAULT_GEMINI_MODEL;
        const modelConfig = GEMINI_MODELS[selectedModel];

        if (!modelConfig) {
            throw new Error(`Unknown model: ${selectedModel}`);
        }

        // Build the full prompt with the custom instructions and text
        const fullPrompt = `${customPrompt}\n\nText to process:\n${text}`;

        const response = await fetch(`${modelConfig.url}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: fullPrompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.3,
                    maxOutputTokens: 4096 // Increased for longer documents
                }
            })
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();
        console.log('Full Gemini API response:', JSON.stringify(data, null, 2));

        if (data.candidates && data.candidates[0]) {
            const candidate = data.candidates[0];
            console.log('Full candidate:', JSON.stringify(candidate, null, 2));

            // Check for finish reason first
            if (candidate.finishReason && candidate.finishReason !== 'STOP') {
                throw new Error(`Gemini API finished with reason: ${candidate.finishReason}. This may indicate content filtering or other issues.`);
            }

            if (candidate.content) {
                const content = candidate.content;
                console.log('Content structure:', content);

                // Handle different response formats
                if (content.parts && content.parts[0] && content.parts[0].text) {
                    const processedText = content.parts[0].text.trim();
                    console.log('Successfully extracted text, length:', processedText.length);
                    return processedText;
                } else if (content.text) {
                    // Alternative format where text is directly in content
                    const processedText = content.text.trim();
                    console.log('Successfully extracted text (alternative format), length:', processedText.length);
                    return processedText;
                } else {
                    console.error('Unexpected content structure:', content);
                    console.error('Content has parts?', !!content.parts);
                    console.error('Parts length:', content.parts?.length);
                    if (content.parts && content.parts[0]) {
                        console.error('First part structure:', content.parts[0]);
                    }
                    throw new Error("Received unexpected content format from AI.");
                }
            } else {
                console.error('Candidate has no content:', candidate);
                throw new Error("AI response candidate has no content.");
            }
        } else {
            console.error('Unexpected response structure:', data);

            // Check for specific error conditions
            if (data.error) {
                throw new Error(`Gemini API error: ${data.error.message || JSON.stringify(data.error)}`);
            }

            throw new Error("Received unexpected response format from AI.");
        }

    } catch (error) {
        console.error('Text processing failed:', error);
        throw error;
    }
}

/**
 * Replace selected text with expanded version
 */
function replaceSelectedText(expandedText) {
    const range = getSelectedRange();
    if (range && dom.abbreviatedTextExpanderTextArea) {
        // Delete the selected content
        range.deleteContents();

        // Insert the expanded text
        const textNode = document.createTextNode(expandedText);
        range.insertNode(textNode);

        // Clear the selection
        window.getSelection().removeAllRanges();

        // Mark document as changed
        handleDocumentChange();

        console.log('Text replaced successfully');
    }
}

/**
 * Update instructions preview
 */
export function updateInstructionsPreview() {
    if (!dom.currentInstructionsPreview) return;
    
    const template = PROMPT_TEMPLATES[currentPromptTemplate] || PROMPT_TEMPLATES.default;
    const selectedModel = dom.geminiModelSelect?.value || DEFAULT_GEMINI_MODEL;
    const modelConfig = GEMINI_MODELS[selectedModel];
    
    let preview = `<div class="instructions-preview">
        <h4>Current Configuration</h4>
        <p><strong>Model:</strong> ${modelConfig?.name || selectedModel}</p>
        <p><strong>Style:</strong> ${template.name}</p>
        <p><strong>Description:</strong> ${template.description}</p>`;
    
    if (customInstructions) {
        preview += `<p><strong>Custom Instructions:</strong> ${customInstructions}</p>`;
    }
    
    preview += `</div>`;
    
    dom.currentInstructionsPreview.innerHTML = preview;
}

// Export prompt templates for other modules
export { PROMPT_TEMPLATES };
export function getCurrentPromptTemplate() { return currentPromptTemplate; }
export function getCustomInstructions() { return customInstructions; }
