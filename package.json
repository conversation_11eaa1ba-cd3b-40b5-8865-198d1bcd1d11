{"name": "web-media-player-doc-editor", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "concurrently \"vite\" \"python scripts/whisper_server.py\" \"node scripts/mistral-auto-server-simple.js\"", "dev:vite-only": "vite", "build": "tsc && vite build", "preview": "vite preview", "whisper": "python scripts/whisper_server.py", "mistral-server": "node scripts/mistral-auto-server-simple.js", "dev:full": "concurrently \"npm run dev:vite-only\" \"npm run whisper\" \"npm run mistral-server\"", "dev:no-pdf": "concurrently \"vite\" \"python scripts/whisper_server.py\"", "start": "npm run dev"}, "dependencies": {"cors": "^2.8.5", "docx": "^9.5.0", "express": "^5.1.0", "pizzip": "^3.2.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "concurrently": "^9.1.2", "typescript": "^5.5.3", "vite": "^5.3.3"}}